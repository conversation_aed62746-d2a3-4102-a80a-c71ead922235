2025-08-01 09:39:29.083 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:39:30.558 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:40:40.817 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 09:40:40.817 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 09:40:40.817 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 09:40:40.818 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 09:40:40.818 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 09:40:40.818 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 09:40:40.818 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 09:40:40.818 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 09:40:40.830 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 09:40:40.830 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 09:40:40.830 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 09:40:40.831 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 09:40:40.831 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 09:40:40.831 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 09:40:40.831 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 09:40:40.831 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 09:40:40.833 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 09:40:40.834 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 09:40:40.837 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 09:40:40.838 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 09:40:40.838 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 09:40:40.895 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 09:40:40.895 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 09:40:40.895 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 09:40:40.896 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 09:40:40.896 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 09:40:40.896 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 09:40:40.896 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 09:40:40.896 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 09:40:41.968 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 09:40:41.968 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 09:40:41.968 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 09:40:54.942 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:40:56.358 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:41:05.431 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:41:06.961 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 09:41:18.355 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1122ms > 1000ms
2025-08-01 09:41:25.778 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1543ms > 1000ms
2025-08-01 09:41:32.936 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1942ms > 1000ms
2025-08-01 09:41:40.294 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1564ms > 1000ms
2025-08-01 09:41:48.135 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1697ms > 1000ms
2025-08-01 09:42:01.079 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1867ms > 1000ms
2025-08-01 09:42:08.522 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1857ms > 1000ms
2025-08-01 09:42:10.095 [ERROR] [websocket.manager] WebSocket客户端 gate_futures 连续出现问题，将尝试重启
2025-08-01 09:42:16.229 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1881ms > 1000ms
2025-08-01 09:42:23.966 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1849ms > 1000ms
2025-08-01 09:42:32.097 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1873ms > 1000ms
2025-08-01 09:42:32.279 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:42:39.246 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1727ms > 1000ms
2025-08-01 09:42:46.727 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1869ms > 1000ms
2025-08-01 09:42:48.789 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:42:54.638 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1831ms > 1000ms
2025-08-01 09:43:01.898 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -1864ms > 1000ms
2025-08-01 09:43:01.913 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:43:02.292 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:43:06.119 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:43:09.224 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -2095ms > 1000ms
2025-08-01 09:43:09.224 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] ⚠️ gate时间偏移过大但继续使用: -2095ms
2025-08-01 09:43:14.021 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 09:43:24.662 [ERROR] [websocket.unified_timestamp_processor.UnifiedTimestampProcessor] 🚨 gate时间偏移异常: -2112ms > 1000ms
