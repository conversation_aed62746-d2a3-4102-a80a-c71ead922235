#!/usr/bin/env python3
"""
🔥 时间戳800ms统一性诊断脚本
精确定位跨交易所时间戳、价格数据时间戳、交易所时间戳的800ms统一性问题
"""

import asyncio
import json
import time
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TimestampDiagnostic:
    """时间戳800ms统一性诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "diagnosis_type": "timestamp_800ms_unified_diagnosis",
            "critical_issues": [],
            "high_issues": [],
            "medium_issues": [],
            "low_issues": [],
            "summary": {},
            "detailed_analysis": {}
        }
        
    async def run_diagnosis(self):
        """运行完整诊断"""
        print("🔥 开始时间戳800ms统一性诊断...")
        
        # 1. 检查跨交易所时间戳同步配置
        await self._check_cross_exchange_sync_config()
        
        # 2. 检查交易所时间戳API实现
        await self._check_exchange_timestamp_apis()
        
        # 3. 检查价格数据时间戳处理
        await self._check_price_data_timestamps()
        
        # 4. 检查WebSocket时间戳处理
        await self._check_websocket_timestamps()
        
        # 5. 检查统一时间戳处理器配置
        await self._check_unified_timestamp_processor()
        
        # 6. 生成诊断报告
        self._generate_summary()
        
        return self.results
    
    async def _check_cross_exchange_sync_config(self):
        """检查跨交易所时间戳同步配置"""
        print("📊 检查跨交易所时间戳同步配置...")
        
        try:
            # 检查配置文件中的800ms设置
            config_files = [
                "config/network_config.py",
                "docs/09_时间同步配置文档.md",
                "core/opportunity_scanner.py"
            ]
            
            config_issues = []
            
            for config_file in config_files:
                file_path = project_root / config_file
                if file_path.exists():
                    content = file_path.read_text(encoding='utf-8')
                    
                    # 检查800ms配置
                    if "800" in content and ("ms" in content or "毫秒" in content):
                        # 查找具体的800ms配置
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if "800" in line and ("ms" in line or "毫秒" in line or "max_diff_ms" in line):
                                config_issues.append({
                                    "file": config_file,
                                    "line": i + 1,
                                    "content": line.strip(),
                                    "type": "800ms_config_found"
                                })
                    
                    # 检查是否还有其他时间戳阈值
                    for threshold in ["100", "500", "1000", "200"]:
                        if f"{threshold}" in content and ("ms" in content or "毫秒" in content):
                            lines = content.split('\n')
                            for i, line in enumerate(lines):
                                if threshold in line and ("ms" in line or "毫秒" in line or "max_diff_ms" in line or "tolerance" in line):
                                    config_issues.append({
                                        "file": config_file,
                                        "line": i + 1,
                                        "content": line.strip(),
                                        "type": f"{threshold}ms_config_found",
                                        "potential_inconsistency": threshold != "800"
                                    })
            
            self.results["detailed_analysis"]["cross_exchange_sync_config"] = config_issues
            
            # 分析配置一致性
            ms_800_count = len([issue for issue in config_issues if issue["type"] == "800ms_config_found"])
            other_ms_count = len([issue for issue in config_issues if issue.get("potential_inconsistency", False)])
            
            if ms_800_count == 0:
                self.results["critical_issues"].append({
                    "type": "MISSING_800MS_CONFIG",
                    "description": "未找到800ms跨交易所时间戳同步配置",
                    "impact": "可能导致时间戳同步阈值不统一",
                    "files_checked": config_files
                })
            
            if other_ms_count > 0:
                self.results["high_issues"].append({
                    "type": "INCONSISTENT_TIMESTAMP_THRESHOLDS",
                    "description": f"发现{other_ms_count}个非800ms的时间戳阈值配置",
                    "impact": "可能导致时间戳同步标准不一致",
                    "inconsistent_configs": [issue for issue in config_issues if issue.get("potential_inconsistency", False)]
                })
                
        except Exception as e:
            self.results["critical_issues"].append({
                "type": "CONFIG_CHECK_ERROR",
                "description": f"检查跨交易所同步配置时出错: {e}",
                "impact": "无法验证配置一致性"
            })
    
    async def _check_exchange_timestamp_apis(self):
        """检查交易所时间戳API实现"""
        print("🏪 检查交易所时间戳API实现...")
        
        try:
            exchange_files = [
                "exchanges/bybit_exchange.py",
                "exchanges/okx_exchange.py", 
                "exchanges/gate_exchange.py"
            ]
            
            api_issues = []
            
            for exchange_file in exchange_files:
                file_path = project_root / exchange_file
                if file_path.exists():
                    content = file_path.read_text(encoding='utf-8')
                    
                    # 检查时间戳处理逻辑
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        # 检查秒级到毫秒级转换
                        if "* 1000" in line and ("time" in line.lower() or "timestamp" in line.lower()):
                            api_issues.append({
                                "file": exchange_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "seconds_to_milliseconds_conversion",
                                "exchange": exchange_file.split('/')[-1].replace('_exchange.py', '')
                            })
                        
                        # 检查毫秒级到秒级转换
                        if "/ 1000" in line and ("time" in line.lower() or "timestamp" in line.lower()):
                            api_issues.append({
                                "file": exchange_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "milliseconds_to_seconds_conversion",
                                "exchange": exchange_file.split('/')[-1].replace('_exchange.py', '')
                            })
                        
                        # 检查纳秒级转换
                        if "// 1000000" in line and ("time" in line.lower() or "timestamp" in line.lower()):
                            api_issues.append({
                                "file": exchange_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "nanoseconds_to_milliseconds_conversion",
                                "exchange": exchange_file.split('/')[-1].replace('_exchange.py', '')
                            })
            
            self.results["detailed_analysis"]["exchange_timestamp_apis"] = api_issues
            
            # 分析API实现一致性
            exchanges_with_conversions = set()
            for issue in api_issues:
                exchanges_with_conversions.add(issue["exchange"])
            
            if len(exchanges_with_conversions) != 3:
                missing_exchanges = {"bybit", "okx", "gate"} - exchanges_with_conversions
                self.results["medium_issues"].append({
                    "type": "INCOMPLETE_TIMESTAMP_CONVERSION",
                    "description": f"部分交易所缺少时间戳转换逻辑: {missing_exchanges}",
                    "impact": "可能导致时间戳单位不一致"
                })
                
        except Exception as e:
            self.results["critical_issues"].append({
                "type": "API_CHECK_ERROR", 
                "description": f"检查交易所API实现时出错: {e}",
                "impact": "无法验证API时间戳处理"
            })
    
    async def _check_price_data_timestamps(self):
        """检查价格数据时间戳处理"""
        print("💰 检查价格数据时间戳处理...")
        
        try:
            # 检查OpportunityScanner中的时间戳处理
            scanner_file = project_root / "core/opportunity_scanner.py"
            if scanner_file.exists():
                content = scanner_file.read_text(encoding='utf-8')
                
                price_timestamp_issues = []
                lines = content.split('\n')
                
                for i, line in enumerate(lines):
                    # 检查800ms配置使用
                    if "800" in line and ("ms" in line or "max_diff_ms" in line):
                        price_timestamp_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "800ms_usage_found"
                        })
                    
                    # 检查数据年龄计算
                    if "data_age" in line or "calculate_data_age" in line:
                        price_timestamp_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "data_age_calculation"
                        })
                    
                    # 检查时间戳同步验证
                    if "validate_cross_exchange_sync" in line:
                        price_timestamp_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "cross_exchange_sync_validation"
                        })
                
                self.results["detailed_analysis"]["price_data_timestamps"] = price_timestamp_issues
                
                # 检查是否正确使用了800ms
                ms_800_usage = [issue for issue in price_timestamp_issues if issue["type"] == "800ms_usage_found"]
                if len(ms_800_usage) == 0:
                    self.results["high_issues"].append({
                        "type": "MISSING_800MS_IN_PRICE_DATA",
                        "description": "OpportunityScanner中未找到800ms时间戳同步配置",
                        "impact": "价格数据时间戳同步可能使用错误的阈值"
                    })
                
        except Exception as e:
            self.results["critical_issues"].append({
                "type": "PRICE_DATA_CHECK_ERROR",
                "description": f"检查价格数据时间戳处理时出错: {e}",
                "impact": "无法验证价格数据时间戳处理"
            })

    async def _check_websocket_timestamps(self):
        """检查WebSocket时间戳处理"""
        print("🌐 检查WebSocket时间戳处理...")

        try:
            websocket_files = [
                "websocket/bybit_ws.py",
                "websocket/okx_ws.py",
                "websocket/gate_ws.py",
                "websocket/unified_timestamp_processor.py"
            ]

            websocket_issues = []

            for ws_file in websocket_files:
                file_path = project_root / ws_file
                if file_path.exists():
                    content = file_path.read_text(encoding='utf-8')
                    lines = content.split('\n')

                    for i, line in enumerate(lines):
                        # 检查get_synced_timestamp使用
                        if "get_synced_timestamp" in line:
                            websocket_issues.append({
                                "file": ws_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "synced_timestamp_usage"
                            })

                        # 检查时间戳转换
                        if ("* 1000" in line or "/ 1000" in line or "// 1000000" in line) and ("time" in line.lower() or "timestamp" in line.lower()):
                            websocket_issues.append({
                                "file": ws_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "timestamp_conversion"
                            })

                        # 检查800ms相关配置
                        if "800" in line and ("ms" in line or "max_diff_ms" in line):
                            websocket_issues.append({
                                "file": ws_file,
                                "line": i + 1,
                                "content": line.strip(),
                                "type": "800ms_config"
                            })

            self.results["detailed_analysis"]["websocket_timestamps"] = websocket_issues

            # 检查统一时间戳处理器使用情况
            synced_timestamp_usage = [issue for issue in websocket_issues if issue["type"] == "synced_timestamp_usage"]
            if len(synced_timestamp_usage) < 3:
                self.results["medium_issues"].append({
                    "type": "INCOMPLETE_SYNCED_TIMESTAMP_USAGE",
                    "description": f"只有{len(synced_timestamp_usage)}个WebSocket文件使用了统一时间戳处理器",
                    "impact": "部分WebSocket可能使用不一致的时间戳处理"
                })

        except Exception as e:
            self.results["critical_issues"].append({
                "type": "WEBSOCKET_CHECK_ERROR",
                "description": f"检查WebSocket时间戳处理时出错: {e}",
                "impact": "无法验证WebSocket时间戳处理"
            })

    async def _check_unified_timestamp_processor(self):
        """检查统一时间戳处理器配置"""
        print("🔧 检查统一时间戳处理器配置...")

        try:
            processor_file = project_root / "websocket/unified_timestamp_processor.py"
            if processor_file.exists():
                content = processor_file.read_text(encoding='utf-8')
                lines = content.split('\n')

                processor_issues = []

                for i, line in enumerate(lines):
                    # 检查800ms配置
                    if "800" in line and ("ms" in line or "max_diff_ms" in line):
                        processor_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "800ms_config_in_processor"
                        })

                    # 检查validate_cross_exchange_sync方法
                    if "validate_cross_exchange_sync" in line:
                        processor_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "cross_exchange_sync_method"
                        })

                    # 检查时间戳单位转换
                    if ("ensure_milliseconds_timestamp" in line or "calculate_data_age" in line):
                        processor_issues.append({
                            "line": i + 1,
                            "content": line.strip(),
                            "type": "timestamp_unit_conversion"
                        })

                self.results["detailed_analysis"]["unified_timestamp_processor"] = processor_issues

                # 检查关键功能是否存在
                has_800ms_config = any(issue["type"] == "800ms_config_in_processor" for issue in processor_issues)
                has_sync_method = any(issue["type"] == "cross_exchange_sync_method" for issue in processor_issues)
                has_unit_conversion = any(issue["type"] == "timestamp_unit_conversion" for issue in processor_issues)

                if not has_800ms_config:
                    self.results["high_issues"].append({
                        "type": "MISSING_800MS_IN_PROCESSOR",
                        "description": "统一时间戳处理器中未找到800ms配置",
                        "impact": "可能使用默认的时间戳同步阈值"
                    })

                if not has_sync_method:
                    self.results["critical_issues"].append({
                        "type": "MISSING_SYNC_METHOD",
                        "description": "统一时间戳处理器中缺少跨交易所同步验证方法",
                        "impact": "无法进行跨交易所时间戳同步验证"
                    })

                if not has_unit_conversion:
                    self.results["high_issues"].append({
                        "type": "MISSING_UNIT_CONVERSION",
                        "description": "统一时间戳处理器中缺少时间戳单位转换功能",
                        "impact": "可能导致秒级和毫秒级时间戳混用"
                    })

        except Exception as e:
            self.results["critical_issues"].append({
                "type": "PROCESSOR_CHECK_ERROR",
                "description": f"检查统一时间戳处理器时出错: {e}",
                "impact": "无法验证统一时间戳处理器配置"
            })

    def _generate_summary(self):
        """生成诊断摘要"""
        total_critical = len(self.results["critical_issues"])
        total_high = len(self.results["high_issues"])
        total_medium = len(self.results["medium_issues"])
        total_low = len(self.results["low_issues"])

        self.results["summary"] = {
            "total_issues": total_critical + total_high + total_medium + total_low,
            "critical_count": total_critical,
            "high_count": total_high,
            "medium_count": total_medium,
            "low_count": total_low,
            "diagnosis_status": "CRITICAL" if total_critical > 0 else "HIGH" if total_high > 0 else "MEDIUM" if total_medium > 0 else "LOW" if total_low > 0 else "HEALTHY"
        }

async def main():
    """主函数"""
    diagnostic = TimestampDiagnostic()
    results = await diagnostic.run_diagnosis()

    # 保存诊断结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"diagnostic_scripts/timestamp_800ms_diagnosis_{timestamp}.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    # 打印诊断结果
    print("\n" + "="*80)
    print("🔥 时间戳800ms统一性诊断结果")
    print("="*80)

    summary = results["summary"]
    print(f"📊 总体状态: {summary['diagnosis_status']}")
    print(f"📈 问题统计: 严重{summary['critical_count']} | 高级{summary['high_count']} | 中级{summary['medium_count']} | 低级{summary['low_count']}")

    if summary["critical_count"] > 0:
        print("\n🚨 严重问题:")
        for issue in results["critical_issues"]:
            print(f"  - {issue['type']}: {issue['description']}")

    if summary["high_count"] > 0:
        print("\n⚠️ 高级问题:")
        for issue in results["high_issues"]:
            print(f"  - {issue['type']}: {issue['description']}")

    if summary["medium_count"] > 0:
        print("\n📋 中级问题:")
        for issue in results["medium_issues"]:
            print(f"  - {issue['type']}: {issue['description']}")

    print(f"\n📄 详细报告已保存到: {output_file}")

    return results

if __name__ == "__main__":
    asyncio.run(main())
