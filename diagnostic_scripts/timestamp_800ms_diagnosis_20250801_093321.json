{"timestamp": "2025-08-01T09:33:21.325047", "diagnosis_type": "timestamp_800ms_unified_diagnosis", "critical_issues": [{"type": "MISSING_800MS_CONFIG", "description": "未找到800ms跨交易所时间戳同步配置", "impact": "可能导致时间戳同步阈值不统一", "files_checked": ["config/network_config.py", "docs/09_时间同步配置文档.md", "core/opportunity_scanner.py"]}], "high_issues": [], "medium_issues": [{"type": "INCOMPLETE_TIMESTAMP_CONVERSION", "description": "部分交易所缺少时间戳转换逻辑: {'bybit', 'okx', 'gate'}", "impact": "可能导致时间戳单位不一致"}, {"type": "INCOMPLETE_SYNCED_TIMESTAMP_USAGE", "description": "只有0个WebSocket文件使用了统一时间戳处理器", "impact": "部分WebSocket可能使用不一致的时间戳处理"}], "low_issues": [], "summary": {"total_issues": 3, "critical_count": 1, "high_count": 0, "medium_count": 2, "low_count": 0, "diagnosis_status": "CRITICAL"}, "detailed_analysis": {"cross_exchange_sync_config": [], "exchange_timestamp_apis": [], "websocket_timestamps": []}}